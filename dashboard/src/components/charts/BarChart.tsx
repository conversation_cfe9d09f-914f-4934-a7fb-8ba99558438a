import React from 'react';
import {
  <PERSON><PERSON><PERSON> as Recha<PERSON><PERSON><PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { Box, Typography, Card, CardContent, useTheme } from '@mui/material';

interface BarChartData {
  name: string;
  [key: string]: string | number;
}

interface BarChartProps {
  title: string;
  data: BarChartData[];
  height?: number;
  showLegend?: boolean;
  showGrid?: boolean;
  xAxisKey?: string;
  yAxisLabel?: string;
  loading?: boolean;
  bars: {
    key: string;
    name: string;
    color?: string;
  }[];
  orientation?: 'vertical' | 'horizontal';
}

const BarChart: React.FC<BarChartProps> = ({
  title,
  data,
  height = 300,
  showLegend = true,
  showGrid = true,
  xAxisKey = 'name',
  yAxisLabel,
  loading = false,
  bars,
  orientation = 'vertical',
}) => {
  const theme = useTheme();

  const defaultColors = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.warning.main,
    theme.palette.error.main,
    theme.palette.info.main,
  ];

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <Box
          sx={{
            bgcolor: 'background.paper',
            border: 1,
            borderColor: 'divider',
            borderRadius: 1,
            p: 1,
            boxShadow: 2,
          }}
        >
          <Typography variant="body2" sx={{ mb: 1, fontWeight: 'bold' }}>
            {label}
          </Typography>
          {payload.map((entry: any, index: number) => (
            <Typography
              key={index}
              variant="body2"
              sx={{ color: entry.color }}
            >
              {entry.name}: {entry.value.toLocaleString()}
            </Typography>
          ))}
        </Box>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {title}
          </Typography>
          <Box
            sx={{
              height,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'text.secondary',
            }}
          >
            Loading chart data...
          </Box>
        </CardContent>
      </Card>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {title}
          </Typography>
          <Box
            sx={{
              height,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'text.secondary',
            }}
          >
            No data available
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {title}
        </Typography>
        <ResponsiveContainer width="100%" height={height}>
          <RechartsBarChart
            data={data}
            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            layout={orientation === 'horizontal' ? 'horizontal' : 'vertical'}
          >
            {showGrid && (
              <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
            )}
            
            {orientation === 'horizontal' ? (
              <>
                <XAxis
                  type="number"
                  stroke={theme.palette.text.secondary}
                  fontSize={12}
                />
                <YAxis
                  type="category"
                  dataKey={xAxisKey}
                  stroke={theme.palette.text.secondary}
                  fontSize={12}
                  width={100}
                />
              </>
            ) : (
              <>
                <XAxis
                  dataKey={xAxisKey}
                  stroke={theme.palette.text.secondary}
                  fontSize={12}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis
                  stroke={theme.palette.text.secondary}
                  fontSize={12}
                  label={yAxisLabel ? { value: yAxisLabel, angle: -90, position: 'insideLeft' } : undefined}
                />
              </>
            )}
            
            <Tooltip content={<CustomTooltip />} />
            {showLegend && <Legend />}
            
            {bars.map((bar, index) => (
              <Bar
                key={bar.key}
                dataKey={bar.key}
                name={bar.name}
                fill={bar.color || defaultColors[index % defaultColors.length]}
                radius={[2, 2, 0, 0]}
              />
            ))}
          </RechartsBarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

export default BarChart;
