import React from 'react';
import {
  Box,
  Typography,
  Chip,
  Tooltip,
  CircularProgress,
  useTheme,
} from '@mui/material';
import {
  CheckCircle,
  Error,
  Warning,
  Info,
  RadioButtonUnchecked,
} from '@mui/icons-material';

export type StatusType = 'healthy' | 'unhealthy' | 'warning' | 'unknown' | 'loading';

interface StatusIndicatorProps {
  status: StatusType;
  label: string;
  description?: string;
  size?: 'small' | 'medium' | 'large';
  variant?: 'chip' | 'icon' | 'dot';
  showLabel?: boolean;
  animate?: boolean;
}

const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  label,
  description,
  size = 'medium',
  variant = 'chip',
  showLabel = true,
  animate = false,
}) => {
  const theme = useTheme();

  const getStatusConfig = () => {
    switch (status) {
      case 'healthy':
        return {
          color: theme.palette.success.main,
          icon: <CheckCircle />,
          chipColor: 'success' as const,
          text: 'Healthy',
        };
      case 'unhealthy':
        return {
          color: theme.palette.error.main,
          icon: <Error />,
          chipColor: 'error' as const,
          text: 'Unhealthy',
        };
      case 'warning':
        return {
          color: theme.palette.warning.main,
          icon: <Warning />,
          chipColor: 'warning' as const,
          text: 'Warning',
        };
      case 'unknown':
        return {
          color: theme.palette.grey[500],
          icon: <RadioButtonUnchecked />,
          chipColor: 'default' as const,
          text: 'Unknown',
        };
      case 'loading':
        return {
          color: theme.palette.info.main,
          icon: <CircularProgress size={16} />,
          chipColor: 'info' as const,
          text: 'Loading',
        };
      default:
        return {
          color: theme.palette.grey[500],
          icon: <Info />,
          chipColor: 'default' as const,
          text: 'Unknown',
        };
    }
  };

  const config = getStatusConfig();

  const getIconSize = () => {
    switch (size) {
      case 'small':
        return 16;
      case 'medium':
        return 20;
      case 'large':
        return 24;
      default:
        return 20;
    }
  };

  const renderChipVariant = () => (
    <Tooltip title={description || ''} arrow>
      <Chip
        icon={React.cloneElement(config.icon, { 
          style: { fontSize: getIconSize() } 
        })}
        label={showLabel ? label : config.text}
        color={config.chipColor}
        variant="outlined"
        size={size === 'large' ? 'medium' : 'small'}
        sx={{
          animation: animate && status === 'loading' ? 'pulse 2s infinite' : 'none',
          '@keyframes pulse': {
            '0%': { opacity: 1 },
            '50%': { opacity: 0.5 },
            '100%': { opacity: 1 },
          },
        }}
      />
    </Tooltip>
  );

  const renderIconVariant = () => (
    <Tooltip title={description || `${label}: ${config.text}`} arrow>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          color: config.color,
          animation: animate && status === 'loading' ? 'pulse 2s infinite' : 'none',
          '@keyframes pulse': {
            '0%': { opacity: 1 },
            '50%': { opacity: 0.5 },
            '100%': { opacity: 1 },
          },
        }}
      >
        {React.cloneElement(config.icon, { 
          style: { fontSize: getIconSize() } 
        })}
        {showLabel && (
          <Typography variant="body2" sx={{ color: 'inherit' }}>
            {label}
          </Typography>
        )}
      </Box>
    </Tooltip>
  );

  const renderDotVariant = () => (
    <Tooltip title={description || `${label}: ${config.text}`} arrow>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Box
          sx={{
            width: getIconSize() * 0.6,
            height: getIconSize() * 0.6,
            borderRadius: '50%',
            backgroundColor: config.color,
            animation: animate ? 'blink 2s infinite' : 'none',
            '@keyframes blink': {
              '0%': { opacity: 1 },
              '50%': { opacity: 0.3 },
              '100%': { opacity: 1 },
            },
          }}
        />
        {showLabel && (
          <Typography variant="body2" color="textSecondary">
            {label}
          </Typography>
        )}
      </Box>
    </Tooltip>
  );

  switch (variant) {
    case 'chip':
      return renderChipVariant();
    case 'icon':
      return renderIconVariant();
    case 'dot':
      return renderDotVariant();
    default:
      return renderChipVariant();
  }
};

export default StatusIndicator;
