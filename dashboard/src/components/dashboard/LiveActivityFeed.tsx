import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardContent,
  Typo<PERSON>,
  Box,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Avatar,
  Divider,
  Button,
} from '@mui/material';
import {
  Activity,
  Link as LinkIcon,
  CheckCircle,
  Error,
  Schedule,
  Refresh,
  Clear,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { useRealTimeLinkUpdates, useRealTimeNotifications } from '../../hooks/useWebSocket';
import { LinkStatus } from '../../types/api';

interface ActivityItem {
  id: string;
  type: 'link_discovered' | 'link_completed' | 'link_failed' | 'system_event';
  message: string;
  timestamp: Date;
  status?: LinkStatus;
  url?: string;
  domain?: string;
}

const LiveActivityFeed: React.FC = () => {
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [maxItems] = useState(50);

  // Real-time updates
  useRealTimeLinkUpdates((linkUpdates) => {
    if (Array.isArray(linkUpdates)) {
      const newActivities = linkUpdates.map(link => ({
        id: `link-${link.id}-${Date.now()}`,
        type: 'link_discovered' as const,
        message: `New link discovered: ${link.domain}`,
        timestamp: new Date(),
        status: link.status,
        url: link.url,
        domain: link.domain,
      }));
      
      addActivities(newActivities);
    }
  });

  useRealTimeNotifications((notification) => {
    const newActivity: ActivityItem = {
      id: `notification-${Date.now()}`,
      type: 'system_event',
      message: notification.message || 'System notification',
      timestamp: new Date(),
    };
    
    addActivities([newActivity]);
  });

  const addActivities = (newActivities: ActivityItem[]) => {
    setActivities(prev => {
      const updated = [...newActivities, ...prev];
      return updated.slice(0, maxItems);
    });
  };

  // Simulate some initial activity
  useEffect(() => {
    const initialActivities: ActivityItem[] = [
      {
        id: '1',
        type: 'system_event',
        message: 'Dashboard initialized',
        timestamp: new Date(),
      },
      {
        id: '2',
        type: 'link_discovered',
        message: 'Crawler service started',
        timestamp: new Date(Date.now() - 60000),
      },
    ];
    
    setActivities(initialActivities);
  }, []);

  const getActivityIcon = (type: string, status?: LinkStatus) => {
    switch (type) {
      case 'link_discovered':
        return <LinkIcon color="primary" />;
      case 'link_completed':
        return <CheckCircle color="success" />;
      case 'link_failed':
        return <Error color="error" />;
      case 'system_event':
        return <Activity color="info" />;
      default:
        return <Schedule color="disabled" />;
    }
  };

  const getStatusColor = (status?: LinkStatus) => {
    switch (status) {
      case LinkStatus.COMPLETED:
        return 'success';
      case LinkStatus.FAILED:
        return 'error';
      case LinkStatus.CRAWLING:
        return 'info';
      case LinkStatus.PENDING:
        return 'warning';
      default:
        return 'default';
    }
  };

  const clearActivities = () => {
    setActivities([]);
  };

  const refreshFeed = () => {
    // In a real implementation, this would trigger a refresh of recent activities
    console.log('Refreshing activity feed...');
  };

  return (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Activity color="primary" />
            <Typography variant="h6">Live Activity</Typography>
            <Chip
              label={activities.length}
              size="small"
              color="primary"
              variant="outlined"
            />
          </Box>
          
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              size="small"
              startIcon={<Refresh />}
              onClick={refreshFeed}
              variant="outlined"
            >
              Refresh
            </Button>
            <Button
              size="small"
              startIcon={<Clear />}
              onClick={clearActivities}
              variant="outlined"
              color="secondary"
            >
              Clear
            </Button>
          </Box>
        </Box>

        <Box sx={{ flexGrow: 1, overflow: 'hidden' }}>
          {activities.length === 0 ? (
            <Box sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center', 
              height: '200px',
              color: 'text.secondary'
            }}>
              <Typography variant="body2">
                No recent activity. Waiting for updates...
              </Typography>
            </Box>
          ) : (
            <List dense sx={{ maxHeight: '400px', overflow: 'auto' }}>
              {activities.map((activity, index) => (
                <React.Fragment key={activity.id}>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon sx={{ minWidth: 40 }}>
                      <Avatar sx={{ width: 32, height: 32, bgcolor: 'transparent' }}>
                        {getActivityIcon(activity.type, activity.status)}
                      </Avatar>
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body2">
                            {activity.message}
                          </Typography>
                          {activity.status && (
                            <Chip
                              label={activity.status}
                              size="small"
                              color={getStatusColor(activity.status)}
                              variant="outlined"
                            />
                          )}
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="caption" color="textSecondary">
                            {format(activity.timestamp, 'HH:mm:ss')}
                          </Typography>
                          {activity.domain && (
                            <Typography variant="caption" color="textSecondary" display="block">
                              Domain: {activity.domain}
                            </Typography>
                          )}
                        </Box>
                      }
                    />
                  </ListItem>
                  {index < activities.length - 1 && <Divider variant="inset" component="li" />}
                </React.Fragment>
              ))}
            </List>
          )}
        </Box>
      </CardContent>
    </Card>
  );
};

export default LiveActivityFeed;
