import React from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Typography,
  Box,
  Chip,
  Divider,
  Grid,
  Card,
  CardContent,
  IconButton,
  Tooltip,
  Link as MuiLink,
} from '@mui/material';
import {
  Close,
  OpenInNew,
  ContentCopy,
  Schedule,
  CheckCircle,
  Error,
  Info,
} from '@mui/icons-material';
import { Link, LinkStatus } from '../../types/api';
import StatusIndicator from '../common/StatusIndicator';

interface LinkDetailsDialogProps {
  open: boolean;
  link: Link | null;
  onClose: () => void;
}

const LinkDetailsDialog: React.FC<LinkDetailsDialogProps> = ({
  open,
  link,
  onClose,
}) => {
  if (!link) return null;

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const getStatusIcon = (status: LinkStatus) => {
    switch (status) {
      case LinkStatus.COMPLETED:
        return <CheckCircle color="success" />;
      case LinkStatus.FAILED:
        return <Error color="error" />;
      case LinkStatus.CRAWLING:
        return <Schedule color="info" />;
      case LinkStatus.PENDING:
        return <Schedule color="warning" />;
      default:
        return <Info color="disabled" />;
    }
  };

  const getStatusColor = (status: LinkStatus) => {
    switch (status) {
      case LinkStatus.COMPLETED:
        return 'success';
      case LinkStatus.FAILED:
        return 'error';
      case LinkStatus.CRAWLING:
        return 'info';
      case LinkStatus.PENDING:
        return 'warning';
      default:
        return 'default';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { minHeight: '60vh' }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6">Link Details</Typography>
          <IconButton onClick={onClose} size="small">
            <Close />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        <Grid container spacing={3}>
          {/* Basic Information */}
          <Grid item xs={12}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Basic Information
                </Typography>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    URL
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <MuiLink
                      href={link.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      sx={{ wordBreak: 'break-all', flexGrow: 1 }}
                    >
                      {link.url}
                    </MuiLink>
                    <Tooltip title="Copy URL">
                      <IconButton size="small" onClick={() => copyToClipboard(link.url)}>
                        <ContentCopy />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Open in new tab">
                      <IconButton size="small" onClick={() => window.open(link.url, '_blank')}>
                        <OpenInNew />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    Domain
                  </Typography>
                  <Typography variant="body1">
                    {link.domain}
                  </Typography>
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    Status
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {getStatusIcon(link.status)}
                    <Chip
                      label={link.status.charAt(0).toUpperCase() + link.status.slice(1)}
                      color={getStatusColor(link.status)}
                      variant="outlined"
                    />
                  </Box>
                </Box>

                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="textSecondary" gutterBottom>
                      Priority
                    </Typography>
                    <Chip
                      label={link.priority}
                      color={link.priority > 0 ? 'primary' : 'default'}
                      variant="outlined"
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="textSecondary" gutterBottom>
                      Retry Count
                    </Typography>
                    <Typography variant="body1">
                      {link.retry_count}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Timestamps */}
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Timestamps
                </Typography>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    Created At
                  </Typography>
                  <Typography variant="body1">
                    {formatDate(link.created_at)}
                  </Typography>
                </Box>

                <Box>
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    Last Updated
                  </Typography>
                  <Typography variant="body1">
                    {formatDate(link.updated_at)}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Metadata */}
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Metadata
                </Typography>
                
                {link.link_metadata && Object.keys(link.link_metadata).length > 0 ? (
                  <Box>
                    {Object.entries(link.link_metadata).map(([key, value]) => (
                      <Box key={key} sx={{ mb: 1 }}>
                        <Typography variant="body2" color="textSecondary">
                          {key}:
                        </Typography>
                        <Typography variant="body2" sx={{ ml: 1, fontFamily: 'monospace' }}>
                          {typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                ) : (
                  <Typography variant="body2" color="textSecondary">
                    No metadata available
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Processing Information */}
          <Grid item xs={12}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Processing Information
                </Typography>
                
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={4}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" color="primary">
                        {link.id}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Link ID
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" color="secondary">
                        {link.priority}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Priority Level
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" color={link.retry_count > 0 ? 'error.main' : 'success.main'}>
                        {link.retry_count}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Retry Attempts
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} variant="outlined">
          Close
        </Button>
        <Button
          onClick={() => window.open(link.url, '_blank')}
          variant="contained"
          startIcon={<OpenInNew />}
        >
          Open URL
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default LinkDetailsDialog;
