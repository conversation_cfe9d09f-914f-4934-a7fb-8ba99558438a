import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Card,
  CardContent,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Divider,
  Alert,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Add,
  Delete,
  Save,
  Cancel,
  Code,
  Preview,
} from '@mui/icons-material';
import { SiteConfig } from '../../types/api';

interface FieldConfig {
  name: string;
  selector: string;
  type: 'text' | 'attribute' | 'html';
  attribute?: string;
}

interface SchemaConfig {
  name: string;
  baseSelector: string;
  fields: FieldConfig[];
}

interface SiteConfigData {
  schema: SchemaConfig;
  crawl_delay: number;
  max_depth: number;
  follow_external_links: boolean;
}

interface SiteConfigFormProps {
  domain: string;
  initialConfig?: SiteConfig;
  onSave: (domain: string, config: SiteConfigData) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

const SiteConfigForm: React.FC<SiteConfigFormProps> = ({
  domain,
  initialConfig,
  onSave,
  onCancel,
  loading = false,
}) => {
  const [config, setConfig] = useState<SiteConfigData>({
    schema: {
      name: `${domain} Schema`,
      baseSelector: 'body',
      fields: [
        { name: 'title', selector: 'h1, .title, title', type: 'text' },
        { name: 'content', selector: '.content, .article-body, main p', type: 'text' },
      ],
    },
    crawl_delay: 1.0,
    max_depth: 3,
    follow_external_links: false,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showPreview, setShowPreview] = useState(false);

  useEffect(() => {
    if (initialConfig) {
      setConfig(initialConfig.config as SiteConfigData);
    }
  }, [initialConfig]);

  const validateConfig = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!config.schema.name.trim()) {
      newErrors.schemaName = 'Schema name is required';
    }

    if (!config.schema.baseSelector.trim()) {
      newErrors.baseSelector = 'Base selector is required';
    }

    if (config.crawl_delay < 0) {
      newErrors.crawlDelay = 'Crawl delay must be non-negative';
    }

    if (config.max_depth < 1) {
      newErrors.maxDepth = 'Max depth must be at least 1';
    }

    config.schema.fields.forEach((field, index) => {
      if (!field.name.trim()) {
        newErrors[`field_${index}_name`] = 'Field name is required';
      }
      if (!field.selector.trim()) {
        newErrors[`field_${index}_selector`] = 'Field selector is required';
      }
      if (field.type === 'attribute' && !field.attribute?.trim()) {
        newErrors[`field_${index}_attribute`] = 'Attribute name is required for attribute type';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (validateConfig()) {
      try {
        await onSave(domain, config);
      } catch (error) {
        console.error('Failed to save config:', error);
      }
    }
  };

  const addField = () => {
    setConfig(prev => ({
      ...prev,
      schema: {
        ...prev.schema,
        fields: [
          ...prev.schema.fields,
          { name: '', selector: '', type: 'text' },
        ],
      },
    }));
  };

  const removeField = (index: number) => {
    setConfig(prev => ({
      ...prev,
      schema: {
        ...prev.schema,
        fields: prev.schema.fields.filter((_, i) => i !== index),
      },
    }));
  };

  const updateField = (index: number, field: Partial<FieldConfig>) => {
    setConfig(prev => ({
      ...prev,
      schema: {
        ...prev.schema,
        fields: prev.schema.fields.map((f, i) => 
          i === index ? { ...f, ...field } : f
        ),
      },
    }));
  };

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Site Configuration: {domain}
      </Typography>

      <Grid container spacing={3}>
        {/* Schema Configuration */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Extraction Schema
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Schema Name"
                    value={config.schema.name}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      schema: { ...prev.schema, name: e.target.value }
                    }))}
                    error={!!errors.schemaName}
                    helperText={errors.schemaName}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Base Selector"
                    value={config.schema.baseSelector}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      schema: { ...prev.schema, baseSelector: e.target.value }
                    }))}
                    error={!!errors.baseSelector}
                    helperText={errors.baseSelector || 'CSS selector for the main content area'}
                  />
                </Grid>
              </Grid>

              <Divider sx={{ my: 3 }} />

              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="subtitle1">
                  Extraction Fields
                </Typography>
                <Button
                  startIcon={<Add />}
                  onClick={addField}
                  variant="outlined"
                  size="small"
                >
                  Add Field
                </Button>
              </Box>

              {config.schema.fields.map((field, index) => (
                <Card key={index} variant="outlined" sx={{ mb: 2 }}>
                  <CardContent>
                    <Grid container spacing={2} alignItems="center">
                      <Grid item xs={12} sm={3}>
                        <TextField
                          fullWidth
                          label="Field Name"
                          value={field.name}
                          onChange={(e) => updateField(index, { name: e.target.value })}
                          error={!!errors[`field_${index}_name`]}
                          helperText={errors[`field_${index}_name`]}
                          size="small"
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <TextField
                          fullWidth
                          label="CSS Selector"
                          value={field.selector}
                          onChange={(e) => updateField(index, { selector: e.target.value })}
                          error={!!errors[`field_${index}_selector`]}
                          helperText={errors[`field_${index}_selector`]}
                          size="small"
                        />
                      </Grid>
                      <Grid item xs={12} sm={2}>
                        <FormControl fullWidth size="small">
                          <InputLabel>Type</InputLabel>
                          <Select
                            value={field.type}
                            label="Type"
                            onChange={(e) => updateField(index, { type: e.target.value as any })}
                          >
                            <MenuItem value="text">Text</MenuItem>
                            <MenuItem value="attribute">Attribute</MenuItem>
                            <MenuItem value="html">HTML</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                      {field.type === 'attribute' && (
                        <Grid item xs={12} sm={2}>
                          <TextField
                            fullWidth
                            label="Attribute"
                            value={field.attribute || ''}
                            onChange={(e) => updateField(index, { attribute: e.target.value })}
                            error={!!errors[`field_${index}_attribute`]}
                            helperText={errors[`field_${index}_attribute`]}
                            size="small"
                          />
                        </Grid>
                      )}
                      <Grid item xs={12} sm={1}>
                        <Tooltip title="Remove Field">
                          <IconButton
                            onClick={() => removeField(index)}
                            color="error"
                            size="small"
                          >
                            <Delete />
                          </IconButton>
                        </Tooltip>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              ))}
            </CardContent>
          </Card>
        </Grid>

        {/* Crawling Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Crawling Settings
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Crawl Delay (seconds)"
                    type="number"
                    value={config.crawl_delay}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      crawl_delay: parseFloat(e.target.value) || 0
                    }))}
                    error={!!errors.crawlDelay}
                    helperText={errors.crawlDelay || 'Delay between requests'}
                    inputProps={{ min: 0, step: 0.1 }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Max Depth"
                    type="number"
                    value={config.max_depth}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      max_depth: parseInt(e.target.value) || 1
                    }))}
                    error={!!errors.maxDepth}
                    helperText={errors.maxDepth || 'Maximum crawling depth'}
                    inputProps={{ min: 1 }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={config.follow_external_links}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          follow_external_links: e.target.checked
                        }))}
                      />
                    }
                    label="Follow External Links"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Preview */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Configuration Preview
                </Typography>
                <Button
                  startIcon={showPreview ? <Code /> : <Preview />}
                  onClick={() => setShowPreview(!showPreview)}
                  size="small"
                >
                  {showPreview ? 'Hide' : 'Show'} JSON
                </Button>
              </Box>

              {showPreview && (
                <TextField
                  fullWidth
                  multiline
                  rows={12}
                  value={JSON.stringify(config, null, 2)}
                  InputProps={{
                    readOnly: true,
                    sx: { fontFamily: 'monospace', fontSize: '0.875rem' }
                  }}
                />
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Actions */}
      <Box sx={{ display: 'flex', gap: 2, mt: 3, justifyContent: 'flex-end' }}>
        <Button
          onClick={onCancel}
          variant="outlined"
          startIcon={<Cancel />}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          startIcon={<Save />}
          disabled={loading}
        >
          {loading ? 'Saving...' : 'Save Configuration'}
        </Button>
      </Box>
    </Box>
  );
};

export default SiteConfigForm;
