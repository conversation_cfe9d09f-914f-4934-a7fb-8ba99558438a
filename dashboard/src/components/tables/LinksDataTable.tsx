import React, { useState, useMemo } from 'react';
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TableSortLabel,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  Typography,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Toolbar,
} from '@mui/material';
import {
  Visibility,
  OpenInNew,
  Refresh,
  FilterList,
  Clear,
} from '@mui/icons-material';
import { Link, LinkStatus } from '../../types/api';
import { SortConfig } from '../../types/dashboard';
import StatusIndicator from '../common/StatusIndicator';

interface LinksDataTableProps {
  links: Link[];
  loading?: boolean;
  onRefresh?: () => void;
  onViewDetails?: (link: Link) => void;
}

type SortableField = 'url' | 'domain' | 'status' | 'priority' | 'created_at' | 'updated_at';

const LinksDataTable: React.FC<LinksDataTableProps> = ({
  links,
  loading = false,
  onRefresh,
  onViewDetails,
}) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    field: 'created_at',
    direction: 'desc',
  });
  
  // Filters
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<LinkStatus | ''>('');
  const [domainFilter, setDomainFilter] = useState('');

  // Get unique domains for filter dropdown
  const uniqueDomains = useMemo(() => {
    const domains = new Set(links.map(link => link.domain));
    return Array.from(domains).sort();
  }, [links]);

  // Apply filters and sorting
  const filteredAndSortedLinks = useMemo(() => {
    let filtered = links.filter(link => {
      const matchesSearch = !searchTerm || 
        link.url.toLowerCase().includes(searchTerm.toLowerCase()) ||
        link.domain.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = !statusFilter || link.status === statusFilter;
      const matchesDomain = !domainFilter || link.domain === domainFilter;
      
      return matchesSearch && matchesStatus && matchesDomain;
    });

    // Sort the filtered results
    filtered.sort((a, b) => {
      const aValue = a[sortConfig.field as keyof Link];
      const bValue = b[sortConfig.field as keyof Link];
      
      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });

    return filtered;
  }, [links, searchTerm, statusFilter, domainFilter, sortConfig]);

  // Paginated data
  const paginatedLinks = useMemo(() => {
    const startIndex = page * rowsPerPage;
    return filteredAndSortedLinks.slice(startIndex, startIndex + rowsPerPage);
  }, [filteredAndSortedLinks, page, rowsPerPage]);

  const handleSort = (field: SortableField) => {
    setSortConfig(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  };

  const handleChangePage = (_: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const clearFilters = () => {
    setSearchTerm('');
    setStatusFilter('');
    setDomainFilter('');
    setPage(0);
  };

  const getStatusColor = (status: LinkStatus) => {
    switch (status) {
      case LinkStatus.COMPLETED:
        return 'success';
      case LinkStatus.FAILED:
        return 'error';
      case LinkStatus.CRAWLING:
        return 'info';
      case LinkStatus.PENDING:
        return 'warning';
      default:
        return 'default';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const truncateUrl = (url: string, maxLength: number = 50) => {
    return url.length > maxLength ? `${url.substring(0, maxLength)}...` : url;
  };

  return (
    <Paper sx={{ width: '100%', overflow: 'hidden' }}>
      {/* Toolbar with filters */}
      <Toolbar sx={{ pl: { sm: 2 }, pr: { xs: 1, sm: 1 } }}>
        <Typography variant="h6" component="div" sx={{ flex: '1 1 100%' }}>
          Links ({filteredAndSortedLinks.length})
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          {/* Search */}
          <TextField
            size="small"
            placeholder="Search URLs or domains..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            sx={{ minWidth: 200 }}
          />
          
          {/* Status Filter */}
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Status</InputLabel>
            <Select
              value={statusFilter}
              label="Status"
              onChange={(e) => setStatusFilter(e.target.value as LinkStatus | '')}
            >
              <MenuItem value="">All</MenuItem>
              {Object.values(LinkStatus).map(status => (
                <MenuItem key={status} value={status}>
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          {/* Domain Filter */}
          <FormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel>Domain</InputLabel>
            <Select
              value={domainFilter}
              label="Domain"
              onChange={(e) => setDomainFilter(e.target.value)}
            >
              <MenuItem value="">All</MenuItem>
              {uniqueDomains.map(domain => (
                <MenuItem key={domain} value={domain}>
                  {domain}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          {/* Clear Filters */}
          <Button
            startIcon={<Clear />}
            onClick={clearFilters}
            variant="outlined"
            size="small"
          >
            Clear
          </Button>
          
          {/* Refresh */}
          <Button
            startIcon={<Refresh />}
            onClick={onRefresh}
            variant="outlined"
            size="small"
            disabled={loading}
          >
            Refresh
          </Button>
        </Box>
      </Toolbar>

      {/* Table */}
      <TableContainer sx={{ maxHeight: 600 }}>
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell>
                <TableSortLabel
                  active={sortConfig.field === 'url'}
                  direction={sortConfig.field === 'url' ? sortConfig.direction : 'asc'}
                  onClick={() => handleSort('url')}
                >
                  URL
                </TableSortLabel>
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={sortConfig.field === 'domain'}
                  direction={sortConfig.field === 'domain' ? sortConfig.direction : 'asc'}
                  onClick={() => handleSort('domain')}
                >
                  Domain
                </TableSortLabel>
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={sortConfig.field === 'status'}
                  direction={sortConfig.field === 'status' ? sortConfig.direction : 'asc'}
                  onClick={() => handleSort('status')}
                >
                  Status
                </TableSortLabel>
              </TableCell>
              <TableCell align="center">
                <TableSortLabel
                  active={sortConfig.field === 'priority'}
                  direction={sortConfig.field === 'priority' ? sortConfig.direction : 'asc'}
                  onClick={() => handleSort('priority')}
                >
                  Priority
                </TableSortLabel>
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={sortConfig.field === 'created_at'}
                  direction={sortConfig.field === 'created_at' ? sortConfig.direction : 'asc'}
                  onClick={() => handleSort('created_at')}
                >
                  Created
                </TableSortLabel>
              </TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {paginatedLinks.map((link) => (
              <TableRow key={link.id} hover>
                <TableCell>
                  <Tooltip title={link.url}>
                    <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                      {truncateUrl(link.url)}
                    </Typography>
                  </Tooltip>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {link.domain}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip
                    label={link.status}
                    color={getStatusColor(link.status)}
                    size="small"
                    variant="outlined"
                  />
                </TableCell>
                <TableCell align="center">
                  <Chip
                    label={link.priority}
                    size="small"
                    variant="outlined"
                    color={link.priority > 0 ? 'primary' : 'default'}
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2" color="textSecondary">
                    {formatDate(link.created_at)}
                  </Typography>
                </TableCell>
                <TableCell align="center">
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Tooltip title="View Details">
                      <IconButton
                        size="small"
                        onClick={() => onViewDetails?.(link)}
                      >
                        <Visibility />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Open URL">
                      <IconButton
                        size="small"
                        onClick={() => window.open(link.url, '_blank')}
                      >
                        <OpenInNew />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      <TablePagination
        rowsPerPageOptions={[10, 25, 50, 100]}
        component="div"
        count={filteredAndSortedLinks.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />
    </Paper>
  );
};

export default LinksDataTable;
