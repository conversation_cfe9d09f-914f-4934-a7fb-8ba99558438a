import { useState, useEffect, useCallback } from 'react';
import { Link, LinksQueryParams, HealthCheck, SiteConfig } from '../types/api';
import { DashboardStats } from '../types/dashboard';
import apiClient from '../services/api';
import StatisticsService from '../services/statistics';

interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

interface UseApiResult<T> extends UseApiState<T> {
  refetch: () => Promise<void>;
}

/**
 * Generic hook for API calls
 */
export function useApi<T>(
  apiCall: () => Promise<T>,
  dependencies: any[] = []
): UseApiResult<T> {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: true,
    error: null,
  });

  const fetchData = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const data = await apiCall();
      setState({ data, loading: false, error: null });
    } catch (error) {
      setState({
        data: null,
        loading: false,
        error: error instanceof Error ? error.message : 'An error occurred',
      });
    }
  }, dependencies);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    ...state,
    refetch: fetchData,
  };
}

/**
 * Hook for fetching links with optional filtering
 */
export function useLinks(params?: LinksQueryParams) {
  return useApi(
    () => apiClient.getLinks(params),
    [JSON.stringify(params)]
  );
}

/**
 * Hook for fetching dashboard statistics
 */
export function useDashboardStats() {
  const { data: links, loading, error, refetch } = useLinks();
  
  const [stats, setStats] = useState<DashboardStats | null>(null);

  useEffect(() => {
    if (links) {
      const calculatedStats = StatisticsService.calculateDashboardStats(links);
      setStats(calculatedStats);
    }
  }, [links]);

  return {
    data: stats,
    loading,
    error,
    refetch,
  };
}

/**
 * Hook for fetching health status
 */
export function useHealth() {
  return useApi(() => apiClient.getHealth());
}

/**
 * Hook for fetching site configuration
 */
export function useSiteConfig(domain: string) {
  return useApi(
    () => apiClient.getSiteConfig(domain),
    [domain]
  );
}

/**
 * Hook for polling data at regular intervals
 */
export function usePolling<T>(
  apiCall: () => Promise<T>,
  interval: number = 5000,
  dependencies: any[] = []
): UseApiResult<T> {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: true,
    error: null,
  });

  const fetchData = useCallback(async () => {
    try {
      const data = await apiCall();
      setState(prev => ({ ...prev, data, loading: false, error: null }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'An error occurred',
      }));
    }
  }, dependencies);

  useEffect(() => {
    // Initial fetch
    fetchData();

    // Set up polling
    const intervalId = setInterval(fetchData, interval);

    return () => clearInterval(intervalId);
  }, [fetchData, interval]);

  const refetch = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    await fetchData();
  }, [fetchData]);

  return {
    ...state,
    refetch,
  };
}

/**
 * Hook for real-time dashboard stats with polling
 */
export function useRealTimeDashboardStats(interval: number = 10000) {
  return usePolling(() => apiClient.getLinks().then(StatisticsService.calculateDashboardStats), interval);
}

/**
 * Hook for real-time health monitoring
 */
export function useRealTimeHealth(interval: number = 30000) {
  return usePolling(() => apiClient.getHealth(), interval);
}
