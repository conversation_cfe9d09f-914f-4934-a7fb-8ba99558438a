import { useEffect, useCallback, useRef } from 'react';
import { WebSocketService, WebSocketEventHandler } from '../services/websocket';

interface UseWebSocketOptions {
  url?: string;
  autoConnect?: boolean;
  reconnectOnClose?: boolean;
}

interface UseWebSocketResult {
  isConnected: boolean;
  connect: () => Promise<void>;
  disconnect: () => void;
  send: (message: any) => void;
  on: (event: string, handler: WebSocketEventHandler) => void;
  off: (event: string, handler: WebSocketEventHandler) => void;
}

/**
 * Hook for managing WebSocket connections
 */
export function useWebSocket(options: UseWebSocketOptions = {}): UseWebSocketResult {
  const {
    url = 'ws://localhost:8000/ws',
    autoConnect = true,
    reconnectOnClose = true,
  } = options;

  const wsRef = useRef<WebSocketService | null>(null);
  const handlersRef = useRef<Map<string, WebSocketEventHandler[]>>(new Map());

  // Initialize WebSocket service
  useEffect(() => {
    wsRef.current = new WebSocketService(url);

    if (autoConnect) {
      wsRef.current.connect().catch(error => {
        console.error('Failed to connect to WebSocket:', error);
      });
    }

    return () => {
      if (wsRef.current) {
        wsRef.current.disconnect();
      }
    };
  }, [url, autoConnect]);

  const connect = useCallback(async () => {
    if (wsRef.current) {
      await wsRef.current.connect();
    }
  }, []);

  const disconnect = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.disconnect();
    }
  }, []);

  const send = useCallback((message: any) => {
    if (wsRef.current) {
      wsRef.current.send(message);
    }
  }, []);

  const on = useCallback((event: string, handler: WebSocketEventHandler) => {
    if (wsRef.current) {
      wsRef.current.on(event, handler);
      
      // Keep track of handlers for cleanup
      if (!handlersRef.current.has(event)) {
        handlersRef.current.set(event, []);
      }
      handlersRef.current.get(event)!.push(handler);
    }
  }, []);

  const off = useCallback((event: string, handler: WebSocketEventHandler) => {
    if (wsRef.current) {
      wsRef.current.off(event, handler);
      
      // Remove from tracked handlers
      const handlers = handlersRef.current.get(event);
      if (handlers) {
        const index = handlers.indexOf(handler);
        if (index > -1) {
          handlers.splice(index, 1);
        }
      }
    }
  }, []);

  const isConnected = wsRef.current?.isConnected() ?? false;

  // Cleanup handlers on unmount
  useEffect(() => {
    return () => {
      if (wsRef.current) {
        handlersRef.current.forEach((handlers, event) => {
          handlers.forEach(handler => {
            wsRef.current!.off(event, handler);
          });
        });
      }
    };
  }, []);

  return {
    isConnected,
    connect,
    disconnect,
    send,
    on,
    off,
  };
}

/**
 * Hook for subscribing to specific WebSocket events
 */
export function useWebSocketEvent(
  event: string,
  handler: WebSocketEventHandler,
  dependencies: any[] = []
) {
  const { on, off } = useWebSocket({ autoConnect: true });

  useEffect(() => {
    on(event, handler);
    
    return () => {
      off(event, handler);
    };
  }, [event, on, off, ...dependencies]);
}

/**
 * Hook for real-time link updates
 */
export function useRealTimeLinkUpdates(onUpdate: (links: any[]) => void) {
  useWebSocketEvent('linkUpdate', onUpdate, [onUpdate]);
}

/**
 * Hook for real-time statistics updates
 */
export function useRealTimeStatsUpdates(onUpdate: (stats: any) => void) {
  useWebSocketEvent('statsUpdate', onUpdate, [onUpdate]);
}

/**
 * Hook for system health updates
 */
export function useRealTimeHealthUpdates(onUpdate: (health: any) => void) {
  useWebSocketEvent('systemHealth', onUpdate, [onUpdate]);
}

/**
 * Hook for notifications
 */
export function useRealTimeNotifications(onNotification: (notification: any) => void) {
  useWebSocketEvent('notification', onNotification, [onNotification]);
}
