import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  TextField,
  DialogActions,
  Button,
} from '@mui/material';
import { SiteConfig } from '../types/api';
import apiClient from '../services/api';
import SiteConfigsList from '../components/tables/SiteConfigsList';
import SiteConfigForm from '../components/forms/SiteConfigForm';

const Configs: React.FC = () => {
  const [configs, setConfigs] = useState<SiteConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingConfig, setEditingConfig] = useState<SiteConfig | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [newDomain, setNewDomain] = useState('');

  // Load configurations (mock implementation since we don't have a list endpoint)
  useEffect(() => {
    loadConfigs();
  }, []);

  const loadConfigs = async () => {
    setLoading(true);
    setError(null);

    try {
      // Since we don't have a list endpoint, we'll simulate with some example domains
      // In a real implementation, you'd call an API endpoint that lists all configurations
      const exampleDomains = ['example.com', 'test.com', 'demo.org'];
      const configPromises = exampleDomains.map(async (domain) => {
        try {
          return await apiClient.getSiteConfig(domain);
        } catch (error) {
          // If config doesn't exist, return null
          return null;
        }
      });

      const results = await Promise.all(configPromises);
      const validConfigs = results.filter((config): config is SiteConfig => config !== null);
      setConfigs(validConfigs);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load configurations');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (config: SiteConfig) => {
    setEditingConfig(config);
    setShowForm(true);
  };

  const handleAdd = () => {
    setAddDialogOpen(true);
  };

  const handleAddConfirm = () => {
    if (newDomain.trim()) {
      setEditingConfig(null);
      setShowForm(true);
      setAddDialogOpen(false);
      setNewDomain('');
    }
  };

  const handleAddCancel = () => {
    setAddDialogOpen(false);
    setNewDomain('');
  };

  const handleDelete = async (domain: string) => {
    try {
      // In a real implementation, you'd call a delete endpoint
      console.log('Deleting config for domain:', domain);
      setConfigs(prev => prev.filter(config => config.domain !== domain));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete configuration');
    }
  };

  const handleView = (config: SiteConfig) => {
    // View functionality is handled in the SiteConfigsList component
    console.log('Viewing config:', config);
  };

  const handleSave = async (domain: string, configData: any) => {
    try {
      const savedConfig = await apiClient.updateSiteConfig(domain, configData);

      // Update the configs list
      setConfigs(prev => {
        const existing = prev.find(c => c.domain === domain);
        if (existing) {
          return prev.map(c => c.domain === domain ? savedConfig : c);
        } else {
          return [...prev, savedConfig];
        }
      });

      setShowForm(false);
      setEditingConfig(null);
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Failed to save configuration');
    }
  };

  const handleCancel = () => {
    setShowForm(false);
    setEditingConfig(null);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        Error loading configurations: {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Site Configurations
      </Typography>

      {showForm ? (
        <SiteConfigForm
          domain={editingConfig?.domain || newDomain}
          initialConfig={editingConfig || undefined}
          onSave={handleSave}
          onCancel={handleCancel}
        />
      ) : (
        <SiteConfigsList
          configs={configs}
          loading={loading}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onAdd={handleAdd}
          onView={handleView}
        />
      )}

      {/* Add Domain Dialog */}
      <Dialog open={addDialogOpen} onClose={handleAddCancel}>
        <DialogTitle>Add New Site Configuration</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Domain"
            fullWidth
            variant="outlined"
            value={newDomain}
            onChange={(e) => setNewDomain(e.target.value)}
            placeholder="example.com"
            helperText="Enter the domain name for the new configuration"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleAddCancel}>Cancel</Button>
          <Button
            onClick={handleAddConfirm}
            variant="contained"
            disabled={!newDomain.trim()}
          >
            Create
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Configs;
