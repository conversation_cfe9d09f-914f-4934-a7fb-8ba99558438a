import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
} from '@mui/material';

const Configs: React.FC = () => {
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Site Configurations
      </Typography>
      
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Coming Soon
          </Typography>
          <Typography variant="body1" color="textSecondary">
            Site configuration management interface will be implemented in the next phase.
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default Configs;
