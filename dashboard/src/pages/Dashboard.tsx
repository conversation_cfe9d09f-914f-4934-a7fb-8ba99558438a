import React from 'react';
import {
  Box,
  Grid,
  Typography,
} from '@mui/material';
import RealTimeStats from '../components/dashboard/RealTimeStats';
import SystemHealthMonitor from '../components/dashboard/SystemHealthMonitor';
import LiveActivityFeed from '../components/dashboard/LiveActivityFeed';

const Dashboard: React.FC = () => {
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Dashboard Overview
      </Typography>

      <Grid container spacing={3}>
        {/* Real-time Statistics */}
        <Grid item xs={12}>
          <RealTimeStats />
        </Grid>

        {/* System Health Monitor */}
        <Grid item xs={12} md={6}>
          <SystemHealthMonitor />
        </Grid>

        {/* Live Activity Feed */}
        <Grid item xs={12} md={6}>
          <LiveActivityFeed />
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
