import React, { useState } from 'react';
import {
  Box,
  Typography,
  CircularProgress,
  Alert,
} from '@mui/material';
import { useLinks } from '../hooks/useApi';
import { Link } from '../types/api';
import LinksDataTable from '../components/tables/LinksDataTable';
import LinkDetailsDialog from '../components/dialogs/LinkDetailsDialog';

const Links: React.FC = () => {
  const { data: links, loading, error, refetch } = useLinks();
  const [selectedLink, setSelectedLink] = useState<Link | null>(null);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);

  const handleViewDetails = (link: Link) => {
    setSelectedLink(link);
    setDetailsDialogOpen(true);
  };

  const handleCloseDetails = () => {
    setDetailsDialogOpen(false);
    setSelectedLink(null);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        Error loading links: {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Links Management
      </Typography>

      {links && links.length > 0 ? (
        <LinksDataTable
          links={links}
          loading={loading}
          onRefresh={refetch}
          onViewDetails={handleViewDetails}
        />
      ) : (
        <Alert severity="info">
          No links discovered yet. Start crawling to see links appear here.
        </Alert>
      )}

      {/* Link Details Dialog */}
      <LinkDetailsDialog
        open={detailsDialogOpen}
        link={selectedLink}
        onClose={handleCloseDetails}
      />
    </Box>
  );
};

export default Links;
