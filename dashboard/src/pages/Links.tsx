import React from 'react';
import {
  Box,
  Typography,
  Card,
  Card<PERSON>ontent,
  CircularProgress,
  Alert,
} from '@mui/material';
import { useLinks } from '../hooks/useApi';

const Links: React.FC = () => {
  const { data: links, loading, error } = useLinks();

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        Error loading links: {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Links Management
      </Typography>
      
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Discovered Links ({links?.length || 0})
          </Typography>
          
          {links && links.length > 0 ? (
            <Box>
              {links.slice(0, 10).map((link) => (
                <Box key={link.id} sx={{ mb: 2, p: 2, border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
                  <Typography variant="body1" noWrap>
                    <strong>URL:</strong> {link.url}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    <strong>Domain:</strong> {link.domain} | <strong>Status:</strong> {link.status} | <strong>Priority:</strong> {link.priority}
                  </Typography>
                  <Typography variant="caption" color="textSecondary">
                    Created: {new Date(link.created_at).toLocaleString()}
                  </Typography>
                </Box>
              ))}
              {links.length > 10 && (
                <Typography variant="body2" color="textSecondary" align="center">
                  ... and {links.length - 10} more links
                </Typography>
              )}
            </Box>
          ) : (
            <Typography variant="body1" color="textSecondary">
              No links discovered yet.
            </Typography>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default Links;
