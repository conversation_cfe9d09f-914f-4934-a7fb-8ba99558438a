// API Response Types based on the FastAPI backend models

export enum LinkStatus {
  PENDING = 'pending',
  CRAWLING = 'crawling',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

export interface SiteConfig {
  domain: string;
  config: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface SiteConfigCreate {
  domain: string;
  config: Record<string, any>;
}

export interface Link {
  id: number;
  url: string;
  domain: string;
  link_metadata: Record<string, any>;
  status: LinkStatus;
  priority: number;
  retry_count: number;
  created_at: string;
  updated_at: string;
}

export interface LinkCreate {
  url: string;
  domain?: string;
  link_metadata?: Record<string, any>;
  priority?: number;
}

export interface DiscoveryRequest {
  urls: string[];
  max_depth?: number;
  site_config?: Record<string, any>;
  priority?: number;
}

export interface DiscoveryResponse {
  discovered_links: number;
  queued_tasks: number;
  message: string;
}

export interface HealthCheck {
  status: string;
  timestamp: string;
  service: string;
  version: string;
  database: string;
}

export interface ReadinessCheck {
  status: string;
  timestamp: string;
}

export interface LivenessCheck {
  status: string;
  timestamp: string;
}

// API Query Parameters
export interface LinksQueryParams {
  domain?: string;
  status?: LinkStatus;
  limit?: number;
  offset?: number;
}

// API Error Response
export interface ApiError {
  detail: string;
}

// Pagination Response
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}
