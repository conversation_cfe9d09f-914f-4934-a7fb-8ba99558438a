"""Main entry point for Crawl Worker Service."""

import logging
import sys
import signal
from .config import settings
from .tasks import app as celery_app

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level.upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)


def signal_handler(signum, frame):
    """Handle shutdown signals gracefully."""
    logger.info(f"Received signal {signum}, shutting down worker...")
    celery_app.control.shutdown()


def main():
    """Main entry point for the worker."""
    logger.info("Starting Crawl Worker Service")
    
    # Register signal handlers
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        # Start Celery worker
        celery_app.worker_main([
            'worker',
            '--loglevel=info',
            '--concurrency=4',
            '--queues=crawl,extract,health',
            '--hostname=crawl-worker@%h'
        ])
    except KeyboardInterrupt:
        logger.info("Worker stopped by user")
    except Exception as e:
        logger.error(f"Worker failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
